[build-system]
requires = ["setuptools>=64", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "lightrag-hku"
dynamic = ["version"]
authors = [
    {name = "<PERSON><PERSON><PERSON> <PERSON>"}
]
description = "LightRAG: Simple and Fast Retrieval-Augmented Generation"
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "aiohttp",
    "configparser",
    "dotenv",
    "future",
    "numpy",
    "pandas>=2.0.0",
    "pipmaster",
    "pydantic",
    "python-dotenv",
    "pyuca",
    "setuptools",
    "tenacity",
    "tiktoken",
    "xlsxwriter>=3.1.0",
]

[project.optional-dependencies]
api = [
    # Core dependencies
    "aiohttp",
    "configparser",
    "dotenv",
    "future",
    "numpy",
    "openai",
    "pandas>=2.0.0",
    "pipmaster",
    "pydantic",
    "python-dotenv",
    "pyuca",
    "setuptools",
    "tenacity",
    "tiktoken",
    "xlsxwriter>=3.1.0",
    # API-specific dependencies
    "aiofiles",
    "ascii_colors",
    "asyncpg",
    "distro",
    "fastapi",
    "httpcore",
    "httpx",
    "jiter",
    "passlib[bcrypt]",
    "PyJWT",
    "python-jose[cryptography]",
    "python-multipart",
    "pytz",
    "uvicorn",
]

[project.scripts]
lightrag-server = "lightrag.api.lightrag_server:main"
lightrag-gunicorn = "lightrag.api.run_with_gunicorn:main"

[project.urls]
Homepage = "https://github.com/HKUDS/LightRAG"
Documentation = "https://github.com/HKUDS/LightRAG"
Repository = "https://github.com/HKUDS/LightRAG"
"Bug Tracker" = "https://github.com/HKUDS/LightRAG/issues"

[tool.setuptools.packages.find]
include = ["lightrag*"]

[tool.setuptools]
include-package-data = true

[tool.setuptools.dynamic]
version = {attr = "lightrag.__version__"}

[tool.setuptools.package-data]
lightrag = ["api/webui/**/*"]

[tool.ruff]
target-version = "py310"
